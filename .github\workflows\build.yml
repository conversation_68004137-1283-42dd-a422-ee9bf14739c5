name: build
on:
  push:
    branches:
      - 'main'
  pull_request:
  workflow_call:
    inputs:
      tag-name:
        required: true
        type: string

jobs:
  build:
    permissions:
      contents: write
    strategy:
      fail-fast: false
      matrix:
        target: [aarch64-unknown-linux-gnu]
        use-cross: [true]
        include:
          - target: x86_64-unknown-linux-gnu
            use-cross: false
          - os: windows-2022
            target: x86_64-pc-windows-msvc
    runs-on: ${{ matrix.os || 'ubuntu-22.04'}}
    env:
      GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      GH_REPO: ${{ github.repository }}
    defaults:
      run:
        shell: bash -xe {0}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: dtolnay/rust-toolchain@stable
        with:
          target: ${{ matrix.target }}
      - uses: Swatinem/rust-cache@98c8021b550208e191a6a3145459bfc9fb29c4c0 # v2.8.0
        with:
          key: ${{ matrix.target }}
      - name: Install cross
        if: ${{ !matrix.os && matrix.use-cross }}
        uses: taiki-e/install-action@d188da0a65bc6c80011567688127bddc8be0ca24 # v2.56.21
        with:
          tool: cross
      - run: ${{ (!matrix.os && matrix.use-cross) && 'cross' || 'cargo' }} build --locked --release --target ${{ matrix.target }}
      - uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: ${{ matrix.target }}
          path: |
            target/${{ matrix.target }}/release/midi-player-stdout
            target/${{ matrix.target }}/release/midi-player-stdout.exe
      - name: Upload to release
        if: ${{ inputs.tag-name }}
        working-directory: target/${{ matrix.target }}/release/
        run: |
          if [ -e midi-player-stdout.exe ]; then
            filename="midi-player-stdout-${{ inputs.tag-name }}-${{ matrix.target }}.exe"
            mv midi-player-stdout.exe "$filename"
            gh release upload ${{ inputs.tag-name }} "$filename"#${{ matrix.target }} --clobber
          else
            filename="midi-player-stdout-${{ inputs.tag-name }}-${{ matrix.target }}"
            mv midi-player-stdout "$filename"
            gh release upload ${{ inputs.tag-name }} "$filename"#${{ matrix.target }} --clobber
          fi
